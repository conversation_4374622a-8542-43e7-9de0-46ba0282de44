import Groq from 'groq-sdk';
import fs from 'fs';
import path from 'path';

class TTSService {
  constructor() {
    this.groq = null;
    this.audioCache = new Map();
  }

  // Lazy initialization of Groq client
  getGroqClient() {
    if (!this.groq) {
      this.groq = new Groq({
        apiKey: process.env.GROQ_API_KEY
      });
    }
    return this.groq;
  }

  async generateSpeech(text, filename = null) {
    try {
      console.log(`[TTS] Generating speech for text: "${text}"`);
      const groq = this.getGroqClient();
      console.log(`[TTS] Groq client initialized, API key: ${process.env.GROQ_API_KEY ? 'present' : 'missing'}`);
      
      const response = await groq.audio.speech.create({
        model: "playai-tts",
        voice: "Basil-PlayAI",
        input: text,
        response_format: "wav"
      });
      
      console.log(`[TTS] Groq API response received`);
      const buffer = Buffer.from(await response.arrayBuffer());
      console.log(`[TTS] Audio buffer size: ${buffer.length} bytes`);
      
      if (filename) {
        // Ensure directory exists
        await fs.promises.mkdir(path.dirname(filename), { recursive: true });
        await fs.promises.writeFile(filename, buffer);
        console.log(`[TTS] Audio saved to: ${filename}`);
        return filename;
      }
      
      return buffer;
    } catch (error) {
      console.error('TTS Generation Error:', error);
      console.error('Error details:', error.message, error.status, error.error);
      throw new Error(`Failed to generate TTS: ${error.message}`);
    }
  }

  async getToolCallAudio() {
    const cacheKey = 'tool_call';
    const cacheFile = path.join(process.cwd(), 'temp', 'tool-call.wav');
    
    // Check memory cache first
    if (this.audioCache.has(cacheKey)) {
      return this.audioCache.get(cacheKey);
    }
    
    try {
      // Try to read from file cache
      await fs.promises.access(cacheFile);
      this.audioCache.set(cacheKey, cacheFile);
      return cacheFile;
    } catch {
      // Generate and cache
      const audioFile = await this.generateSpeech("this is a tool call", cacheFile);
      this.audioCache.set(cacheKey, audioFile);
      return audioFile;
    }
  }

  async getEnableSound() {
    const cacheKey = 'enabled';
    const cacheFile = path.join(process.cwd(), 'temp', 'enabled.wav');
    
    // Check memory cache first
    if (this.audioCache.has(cacheKey)) {
      return this.audioCache.get(cacheKey);
    }
    
    try {
      // Try to read from file cache
      await fs.promises.access(cacheFile);
      this.audioCache.set(cacheKey, cacheFile);
      return cacheFile;
    } catch {
      // Generate and cache
      const audioFile = await this.generateSpeech("enabled", cacheFile);
      this.audioCache.set(cacheKey, audioFile);
      return audioFile;
    }
  }

  // Generate TTS for any text with unique filename (no caching for voice_response)
  async generateCustomSpeech(text, useRandomFilename = true) {
    if (useRandomFilename) {
      // Clean up old voice files first (keep only last 10)
      await this.cleanupOldVoiceFiles();
      
      // Generate unique filename for each request
      const timestamp = Date.now();
      const randomId = Math.random().toString(36).substring(2, 8);
      const filename = path.join(process.cwd(), 'temp', `voice-${timestamp}-${randomId}.wav`);
      
      console.log(`[TTS] Generating custom speech with unique file: ${filename}`);
      return await this.generateSpeech(text, filename);
    } else {
      // Return buffer directly (no file caching)
      return await this.generateSpeech(text);
    }
  }

  // Clean up old voice-*.wav files to prevent disk space issues
  async cleanupOldVoiceFiles() {
    try {
      const tempDir = path.join(process.cwd(), 'temp');
      const files = await fs.promises.readdir(tempDir);
      const voiceFiles = files
        .filter(file => file.startsWith('voice-') && file.endsWith('.wav'))
        .map(file => ({
          name: file,
          path: path.join(tempDir, file),
          stat: fs.statSync(path.join(tempDir, file))
        }))
        .sort((a, b) => b.stat.mtime.getTime() - a.stat.mtime.getTime()); // Sort by modified time, newest first
      
      // Keep only the 10 most recent files, delete the rest
      if (voiceFiles.length > 10) {
        const filesToDelete = voiceFiles.slice(10);
        for (const file of filesToDelete) {
          await fs.promises.unlink(file.path);
          console.log(`[TTS] Cleaned up old voice file: ${file.name}`);
        }
      }
    } catch (error) {
      console.error('[TTS] Error cleaning up old voice files:', error);
      // Don't throw - this shouldn't stop TTS generation
    }
  }

  // Clear cache to free memory if needed
  clearCache() {
    this.audioCache.clear();
  }

  // Get cache size for monitoring
  getCacheInfo() {
    return {
      size: this.audioCache.size,
      keys: Array.from(this.audioCache.keys())
    };
  }
}

export default new TTSService();