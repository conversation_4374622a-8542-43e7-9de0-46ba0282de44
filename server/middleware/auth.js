import jwt from 'jsonwebtoken';
import { userDb } from '../database/db.js';

// Get JWT secret from environment or use default (for development)
const JWT_SECRET = process.env.JWT_SECRET || 'claude-ui-dev-secret-change-in-production';

// Optional API key middleware - DISABLED
const validateApiKey = (req, res, next) => {
  // API key validation disabled - always allow access
  next();
};

// JWT authentication middleware - DISABLED
const authenticateToken = async (req, res, next) => {
  // Authentication disabled - always allow access
  req.user = { id: 1, username: 'anonymous' }; // Mock user for compatibility
  next();
};

// Generate JWT token (never expires)
const generateToken = (user) => {
  return jwt.sign(
    { 
      userId: user.id, 
      username: user.username 
    },
    JWT_SECRET
    // No expiration - token lasts forever
  );
};

// WebSocket authentication function - DISABLED
const authenticateWebSocket = (token) => {
  // Authentication disabled - always return mock user
  return { userId: 1, username: 'anonymous' };
};

export {
  validateApiKey,
  authenticateToken,
  generateToken,
  authenticateWebSocket,
  JWT_SECRET
};