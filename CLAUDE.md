# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Claude Code UI is a web-based interface for Claude Code CLI that provides a responsive chat interface, file explorer, git integration, and session management. The application consists of a Node.js/Express backend with WebSocket support and a React frontend built with Vite.

## Development Commands

### Core Development
- `npm run dev` - Start development environment (runs both client and server concurrently)
- `npm run server` - Start only the backend server 
- `npm run client` - Start only the frontend development server
- `npm run build` - Build the React frontend for production
- `npm run start` - Build and start production server

### Project Structure
- Backend server runs on port 3000 (configurable via PORT env var)
- Frontend development server runs on port 3001 (configurable via VITE_PORT env var)
- Production serves built React app from the Express server

## Architecture

### Backend (server/)
The Express server handles multiple concerns:

**Core Components:**
- `server/index.js` - Main server file with WebSocket handling, API routes, and static file serving
- `server/projects.js` - <PERSON> projects management with JSONL parsing and session extraction
- `server/claude-cli.js` - Claude CL<PERSON> process spawning and management
- `server/middleware/auth.js` - Authentication middleware (currently disabled)

**Key Features:**
- **Dual WebSocket Endpoints**: `/ws` for chat communication, `/shell` for terminal access
- **Project Discovery**: Automatically scans `~/.claude/projects/` and parses JSONL session files
- **File System API**: Read/write project files with security checks for absolute paths only
- **Real-time Updates**: Chokidar-based file watching with debounced project updates
- **Session Protection System**: Prevents sidebar updates during active conversations

### Frontend (src/)
React 18 application with modern hooks and routing:

**Core Components:**
- `src/App.jsx` - Main application with sophisticated session protection logic
- `src/components/Sidebar.jsx` - Project and session management interface
- `src/components/MainContent.jsx` - Tabbed interface for chat, files, and git
- `src/components/ChatInterface.jsx` - Real-time chat with Claude CLI integration
- `src/components/Shell.jsx` - Terminal interface using xterm.js and node-pty

**Architecture Patterns:**
- Context-based theme management (`contexts/ThemeContext.jsx`)
- Custom WebSocket hook (`utils/websocket.js`)
- API abstraction layer (`utils/api.js`)
- Responsive design with mobile-specific navigation

### Session Protection System
Critical feature in `App.jsx` that tracks active conversations to prevent WebSocket project updates from clearing chat messages:
- Marks sessions as "active" when user sends messages
- Pauses project updates during conversations
- Handles both existing session IDs and temporary session identifiers
- Allows additive updates (new sessions) while protecting active sessions

## Key Implementation Details

### Project Management
- Projects are discovered from `~/.claude/projects/` directory structure
- Display names generated from `package.json` or path-based fallbacks
- Custom project naming stored in `~/.claude/project-config.json`
- Session parsing extracts metadata from Claude CLI JSONL files
- Project directory extraction uses `cwd` analysis from session history

### WebSocket Communication
Two separate WebSocket endpoints handle different use cases:
- **Chat WebSocket** (`/ws`): Handles claude-command messages and real-time chat
- **Shell WebSocket** (`/shell`): Provides full terminal access with PTY support

### File Operations
- All file paths must be absolute for security
- File tree generation with configurable depth limits
- Automatic backup creation before file saves
- Support for binary file serving (images, etc.)

### Authentication Status
Authentication is currently **disabled** throughout the application:
- All routes bypass authentication middleware
- WebSocket connections auto-approve with anonymous user
- Auth-related components and routes are commented out

## Environment Configuration

The application uses environment variables for configuration:
- `PORT` - Backend server port (default: 3000)
- `VITE_PORT` - Frontend dev server port (default: 3001) 
- `OPENAI_API_KEY` - Required for audio transcription features
- Authentication-related variables are present but unused

## Testing and Quality
No specific test commands are defined in package.json. The application relies on runtime testing and manual validation during development.