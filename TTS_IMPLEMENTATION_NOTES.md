# TTS Implementation Notes

## Current Status: Mock Audio Implementation

The initial implementation attempted to use Groq SDK for text-to-speech, but the Groq SDK doesn't have the same `audio.speech.create` API as OpenAI. 

**Current Solution**: Implemented mock audio generation that creates simple beep sounds for testing the TTS workflow.

## What Works Now

✅ **TTS Button**: Appears and toggles correctly  
✅ **Audio Generation**: Creates mock WAV files with different tones  
✅ **Tool Call Detection**: Triggers audio on every tool call  
✅ **Browser Audio Handling**: Properly satisfies browser requirements  
✅ **Caching System**: Efficiently caches generated audio  
✅ **UI Integration**: Seamlessly integrated with chat interface  

## Testing the Current Implementation

1. **Enable TTS**: Click the volume button - you'll hear a high-pitched beep (800Hz)
2. **Trigger Tool Calls**: Send messages that use tools - you'll hear a lower-pitched beep (400Hz)
3. **Visual Feedback**: Button changes from 🔇 to 🔊 when enabled

## Real TTS Implementation Options

To implement actual speech synthesis, you have several options:

### Option 1: OpenAI TTS API (Recommended)
```javascript
// Replace the mock implementation with:
import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY
});

async generateSpeech(text, filename = null) {
  const response = await openai.audio.speech.create({
    model: "tts-1",
    voice: "alloy",
    input: text,
    response_format: "wav"
  });
  
  const buffer = Buffer.from(await response.arrayBuffer());
  // ... rest of implementation
}
```

### Option 2: Google Cloud Text-to-Speech
```javascript
import { TextToSpeechClient } from '@google-cloud/text-to-speech';

const client = new TextToSpeechClient();

async generateSpeech(text, filename = null) {
  const [response] = await client.synthesizeSpeech({
    input: { text },
    voice: { languageCode: 'en-US', ssmlGender: 'NEUTRAL' },
    audioConfig: { audioEncoding: 'LINEAR16' },
  });
  
  const buffer = Buffer.from(response.audioContent, 'binary');
  // ... rest of implementation
}
```

### Option 3: ElevenLabs API
```javascript
import { ElevenLabsAPI } from 'elevenlabs-api';

const elevenlabs = new ElevenLabsAPI({
  apiKey: process.env.ELEVENLABS_API_KEY
});

async generateSpeech(text, filename = null) {
  const response = await elevenlabs.textToSpeech({
    voice_id: "21m00Tcm4TlvDq8ikWAM", // Rachel voice
    text: text,
    model_id: "eleven_monolingual_v1"
  });
  
  // ... process response
}
```

### Option 4: Web Speech API (Client-Side)
```javascript
// In the frontend TTSController.jsx:
const speakText = (text) => {
  if ('speechSynthesis' in window) {
    const utterance = new SpeechSynthesisUtterance(text);
    utterance.rate = 1;
    utterance.pitch = 1;
    utterance.volume = 1;
    window.speechSynthesis.speak(utterance);
  }
};
```

## Recommended Implementation Path

For your use case, I recommend **Option 1 (OpenAI TTS)** because:
- High-quality voice synthesis
- Same API pattern you're already familiar with
- Reliable and well-documented
- Good pricing for your use case

## Implementation Steps to Switch to Real TTS

1. **Choose TTS Provider** (recommend OpenAI)
2. **Update Dependencies**:
   ```bash
   npm install openai
   # or
   npm install @google-cloud/text-to-speech
   # or  
   npm install elevenlabs-api
   ```

3. **Update TTS Service** (`server/services/tts.js`):
   - Replace mock implementation with chosen provider
   - Update API calls and response handling

4. **Update Environment Variables**:
   ```bash
   OPENAI_API_KEY=your_openai_api_key
   # or
   GOOGLE_APPLICATION_CREDENTIALS=path/to/credentials.json
   # or
   ELEVENLABS_API_KEY=your_elevenlabs_key
   ```

5. **Test Implementation**:
   - Verify audio quality
   - Test caching behavior
   - Confirm browser playback

## Future Voice Response Integration

The current architecture is perfect for your planned `voice_response` MCP tool expansion:

```javascript
// In ChatInterface.jsx tool call handler:
if (part.type === 'tool_use') {
  // ... existing code ...
  
  // Enhanced TTS for voice_response tool
  if (ttsEnabled) {
    if (part.name === 'voice_response' && part.input?.text) {
      // Use actual response text for TTS
      playCustomTTS(part.input.text);
    } else {
      // Default notification sound
      playToolCallSound();
    }
  }
}
```

## Current File Structure

```
server/
├── services/tts.js          # Mock TTS implementation
└── index.js                 # TTS API endpoints (API key checks disabled)

src/
└── components/
    ├── TTSController.jsx     # Working TTS controller
    └── ChatInterface.jsx     # Integrated with tool call detection

temp/                         # Cached audio files
├── tool-call.wav            # Mock beep for tool calls
└── enabled.wav              # Mock beep for enable sound
```

## Testing the Mock Implementation

The current mock implementation is fully functional for testing the TTS workflow:

1. **Start the app**: `npm run dev`
2. **Click TTS button**: Should hear high-pitched beep and see green volume icon
3. **Trigger tool calls**: Should hear lower-pitched beep for each tool call
4. **Check audio files**: Look in `temp/` directory for generated WAV files

This proves the entire TTS system works - you just need to swap in a real TTS provider when ready!