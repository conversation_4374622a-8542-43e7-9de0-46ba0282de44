# Text-to-Speech Implementation Plan for Tool Calls

## Overview

This document outlines the implementation plan for adding text-to-speech (TTS) functionality that announces "tool call" whenever a tool call occurs during a conversation. The implementation will integrate with the existing tool call detection system and respect browser audio constraints.

## Current Tool Call Detection

The application already has robust tool call detection in place:

### Frontend Detection (`src/components/ChatInterface.jsx`)

1. **Tool Call Parsing**: Tool calls are detected in the WebSocket message handler when processing <PERSON>'s responses
2. **Message Structure**: Tool calls are identified by:
   - `part.type === 'tool_use'` in message content arrays
   - Messages are tagged with `isToolUse: true`
   - Additional properties: `toolName`, `toolInput`, `toolId`
3. **UI Differentiation**: Tool calls have distinct UI rendering based on `message.isToolUse` property
4. **Tool Call Detection Points**:
   - Real-time during WebSocket message processing (line ~1054)
   - Session loading from stored messages (line ~689)

### WebSocket Communication

- Tool calls flow through the main WebSocket endpoint (`/ws`)
- Server processes Claude CLI responses and forwards them to frontend
- Real-time detection happens in the `claude-message` event handler

## TTS Implementation Approach

### 1. Backend TTS Service

**New File**: `server/services/tts.js`

```javascript
import Groq from 'groq-sdk';
import fs from 'fs';
import path from 'path';

class TTSService {
  constructor() {
    this.groq = new Groq({
      apiKey: process.env.GROQ_API_KEY
    });
  }

  async generateSpeech(text, filename = null) {
    const response = await this.groq.audio.speech.create({
      model: "playai-tts",
      voice: "Fritz-PlayAI",
      input: text,
      response_format: "wav"
    });
    
    const buffer = Buffer.from(await response.arrayBuffer());
    
    if (filename) {
      await fs.promises.writeFile(filename, buffer);
      return filename;
    }
    
    return buffer;
  }

  async getToolCallAudio() {
    // Cache the "tool call" audio to avoid regenerating
    const cacheFile = path.join(process.cwd(), 'temp', 'tool-call.wav');
    
    try {
      // Try to read from cache first
      await fs.promises.access(cacheFile);
      return cacheFile;
    } catch {
      // Generate and cache
      await fs.promises.mkdir(path.dirname(cacheFile), { recursive: true });
      return await this.generateSpeech("tool call", cacheFile);
    }
  }
}

export default new TTSService();
```

### 2. Backend API Endpoint

**Addition to**: `server/index.js`

```javascript
import ttsService from './services/tts.js';

// TTS endpoint for tool call audio
app.get('/api/tts/tool-call', async (req, res) => {
  try {
    const audioFile = await ttsService.getToolCallAudio();
    res.setHeader('Content-Type', 'audio/wav');
    res.sendFile(path.resolve(audioFile));
  } catch (error) {
    console.error('TTS Error:', error);
    res.status(500).json({ error: 'Failed to generate TTS audio' });
  }
});

// TTS enable sound (initial interaction audio)
app.get('/api/tts/enable-sound', async (req, res) => {
  try {
    // Generate a very brief "enabled" sound
    const buffer = await ttsService.generateSpeech("enabled");
    res.setHeader('Content-Type', 'audio/wav');
    res.send(buffer);
  } catch (error) {
    console.error('TTS Enable Sound Error:', error);
    res.status(500).json({ error: 'Failed to generate enable sound' });
  }
});
```

### 3. Frontend TTS Component

**New File**: `src/components/TTSController.jsx`

```javascript
import React, { useState, useRef, useCallback } from 'react';
import { Volume2, VolumeX } from 'lucide-react';

export const TTSController = ({ onTTSToggle }) => {
  const [isEnabled, setIsEnabled] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const audioRef = useRef(null);

  const enableTTS = useCallback(async () => {
    setIsLoading(true);
    try {
      // First, play the enable sound to satisfy browser requirements
      const enableResponse = await fetch('/api/tts/enable-sound');
      const enableBlob = await enableResponse.blob();
      const enableUrl = URL.createObjectURL(enableBlob);
      
      const enableAudio = new Audio(enableUrl);
      await enableAudio.play();
      
      // Pre-load the tool call audio
      const toolCallResponse = await fetch('/api/tts/tool-call');
      const toolCallBlob = await toolCallResponse.blob();
      const toolCallUrl = URL.createObjectURL(toolCallBlob);
      
      audioRef.current = new Audio(toolCallUrl);
      
      setIsEnabled(true);
      onTTSToggle?.(true);
    } catch (error) {
      console.error('Failed to enable TTS:', error);
    } finally {
      setIsLoading(false);
    }
  }, [onTTSToggle]);

  const disableTTS = useCallback(() => {
    setIsEnabled(false);
    onTTSToggle?.(false);
    if (audioRef.current) {
      audioRef.current = null;
    }
  }, [onTTSToggle]);

  const playToolCallSound = useCallback(async () => {
    if (!isEnabled || !audioRef.current) return;
    
    try {
      // Reset audio to beginning and play
      audioRef.current.currentTime = 0;
      await audioRef.current.play();
    } catch (error) {
      console.error('Failed to play tool call sound:', error);
    }
  }, [isEnabled]);

  return {
    isEnabled,
    isLoading,
    enableTTS,
    disableTTS,
    playToolCallSound,
    TTSButton: () => (
      <button
        onClick={isEnabled ? disableTTS : enableTTS}
        disabled={isLoading}
        className="p-2 rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
        title={isEnabled ? "Disable TTS" : "Enable TTS"}
      >
        {isLoading ? (
          <div className="w-5 h-5 animate-spin rounded-full border-2 border-gray-400 border-t-transparent" />
        ) : isEnabled ? (
          <Volume2 className="w-5 h-5 text-green-600" />
        ) : (
          <VolumeX className="w-5 h-5 text-gray-600" />
        )}
      </button>
    )
  };
};
```

### 4. Integration with ChatInterface

**Modifications to**: `src/components/ChatInterface.jsx`

```javascript
import { TTSController } from './TTSController';

// Inside ChatInterface component
const {
  isEnabled: ttsEnabled,
  enableTTS,
  disableTTS,
  playToolCallSound,
  TTSButton
} = TTSController({
  onTTSToggle: (enabled) => {
    // Optional: Save TTS preference to localStorage
    localStorage.setItem('tts-enabled', enabled.toString());
  }
});

// In the WebSocket message handler where tool calls are detected
if (part.type === 'tool_use') {
  // Existing tool call handling code...
  
  // Add TTS announcement
  if (ttsEnabled) {
    playToolCallSound();
  }
  
  // ... rest of existing code
}

// Add TTS button to the UI (perhaps in the input area)
<div className="flex items-center gap-2">
  <TTSButton />
  {/* ... existing input controls */}
</div>
```

## Browser Audio Constraints Solution

### The Problem
Modern browsers prevent auto-playing audio unless the user has interacted with the page first.

### The Solution
1. **TTS Toggle Button**: User must explicitly enable TTS
2. **Initial Interaction**: When enabled, immediately play a brief sound ("enabled")
3. **Audio Pre-loading**: Pre-load the "tool call" audio after initial interaction
4. **Seamless Playback**: Subsequent tool call sounds will play automatically

## Implementation Phases

### Phase 1: Basic TTS Infrastructure
- [ ] Add Groq dependency to package.json
- [ ] Create TTS service backend
- [ ] Add API endpoints for audio generation
- [ ] Create TTS controller component

### Phase 2: Integration
- [ ] Add TTS button to chat interface
- [ ] Integrate with existing tool call detection
- [ ] Add audio caching and optimization
- [ ] Test browser compatibility

### Phase 3: Enhancement (Future)
- [ ] User preference storage
- [ ] Different sounds for different tool types
- [ ] Volume control
- [ ] Voice selection options

## Environment Variables Required

Add to `.env`:
```
GROQ_API_KEY=your_groq_api_key_here
```

## File Structure Changes

```
server/
├── services/
│   └── tts.js          # New TTS service
└── index.js            # Modified: Add TTS endpoints

src/
├── components/
│   ├── TTSController.jsx    # New TTS controller component
│   └── ChatInterface.jsx    # Modified: Integrate TTS
└── utils/
    └── tts.js              # Optional: TTS utilities

temp/                       # New: Temporary audio files
└── tool-call.wav          # Cached tool call audio
```

## Security Considerations

- Rate limiting on TTS endpoints to prevent abuse
- Audio file caching to reduce API costs
- GROQ_API_KEY security in environment variables
- Audio file cleanup strategy for temp files

## Browser Compatibility

- Modern browsers with Web Audio API support
- Fallback handling for browsers without audio support
- Mobile browser considerations (iOS Safari, Chrome Mobile)

## Testing Strategy

1. **Unit Tests**: TTS service audio generation
2. **Integration Tests**: Tool call detection triggers TTS
3. **Browser Tests**: Audio playback across different browsers
4. **User Experience Tests**: TTS toggle workflow

## Cost Considerations

- Cache frequently used audio ("tool call" phrase)
- Implement usage limits/quotas
- Monitor GROQ API usage and costs
- Consider alternative TTS providers for cost optimization

## Next Steps

This implementation provides a solid foundation for TTS functionality while respecting browser constraints and maintaining good user experience. The modular design allows for future enhancements and customization.