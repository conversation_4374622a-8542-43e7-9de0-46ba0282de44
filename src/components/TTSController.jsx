import React, { useState, useRef, useCallback } from 'react';
import { Volume2, VolumeX } from 'lucide-react';

export const TTSController = ({ onTTSToggle }) => {
  const [isEnabled, setIsEnabled] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const audioRef = useRef(null);

  const enableTTS = useCallback(async () => {
    setIsLoading(true);
    try {
      // First, play the enable sound to satisfy browser requirements
      const enableResponse = await fetch('/api/tts/enable-sound');
      if (!enableResponse.ok) {
        throw new Error(`Enable sound failed: ${enableResponse.status}`);
      }
      
      const enableBlob = await enableResponse.blob();
      const enableUrl = URL.createObjectURL(enableBlob);
      
      const enableAudio = new Audio(enableUrl);
      await enableAudio.play();
      
      // Pre-load the tool call audio
      const toolCallResponse = await fetch('/api/tts/tool-call');
      if (!toolCallResponse.ok) {
        throw new Error(`Tool call audio failed: ${toolCallResponse.status}`);
      }
      
      const toolCallBlob = await toolCallResponse.blob();
      const toolCallUrl = URL.createObjectURL(toolCallBlob);
      
      audioRef.current = new Audio(toolCallUrl);
      
      // Clean up the enable audio URL
      URL.revokeObjectURL(enableUrl);
      
      setIsEnabled(true);
      onTTSToggle?.(true);
      
      // Save preference to localStorage
      localStorage.setItem('tts-enabled', 'true');
    } catch (error) {
      console.error('Failed to enable TTS:', error);
      alert(`Failed to enable TTS: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  }, [onTTSToggle]);

  const disableTTS = useCallback(() => {
    setIsEnabled(false);
    onTTSToggle?.(false);
    
    if (audioRef.current) {
      // Clean up the audio URL
      const audioSrc = audioRef.current.src;
      if (audioSrc.startsWith('blob:')) {
        URL.revokeObjectURL(audioSrc);
      }
      audioRef.current = null;
    }
    
    // Save preference to localStorage
    localStorage.setItem('tts-enabled', 'false');
  }, [onTTSToggle]);

  const playToolCallSound = useCallback(async () => {
    if (!isEnabled || !audioRef.current) {
      return;
    }
    
    try {
      // Reset audio to beginning and play
      audioRef.current.currentTime = 0;
      await audioRef.current.play();
    } catch (error) {
      console.error('Failed to play tool call sound:', error);
      // Don't show user error for audio playback failures
    }
  }, [isEnabled]);

  const playCustomTTS = useCallback(async (text) => {
    if (!isEnabled || !text || text.trim().length === 0) {
      return;
    }
    
    try {
      // Generate custom TTS audio
      const response = await fetch('/api/tts/custom', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ text: text.trim() })
      });
      
      if (!response.ok) {
        throw new Error(`Custom TTS failed: ${response.status}`);
      }
      
      const audioBlob = await response.blob();
      const audioUrl = URL.createObjectURL(audioBlob);
      const customAudio = new Audio(audioUrl);
      
      // Play the custom audio
      await customAudio.play();
      
      // Clean up the URL after playing
      customAudio.onended = () => {
        URL.revokeObjectURL(audioUrl);
      };
    } catch (error) {
      console.error('Failed to play custom TTS:', error);
      // Fallback to default tool call sound
      playToolCallSound();
    }
  }, [isEnabled, playToolCallSound]);

  // Initialize TTS state from localStorage on component mount
  React.useEffect(() => {
    const savedState = localStorage.getItem('tts-enabled');
    if (savedState === 'true') {
      // Don't auto-enable, user must click button due to browser restrictions
      // But we can show the last preference
    }
  }, []);

  const TTSButton = () => (
    <button
      onClick={isEnabled ? disableTTS : enableTTS}
      disabled={isLoading}
      className="p-2 rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
      title={isEnabled ? "Disable TTS" : "Enable TTS"}
      type="button"
    >
      {isLoading ? (
        <div className="w-5 h-5 animate-spin rounded-full border-2 border-gray-400 border-t-transparent" />
      ) : isEnabled ? (
        <Volume2 className="w-5 h-5 text-green-600" />
      ) : (
        <VolumeX className="w-5 h-5 text-gray-600 dark:text-gray-400" />
      )}
    </button>
  );

  return {
    isEnabled,
    isLoading,
    enableTTS,
    disableTTS,
    playToolCallSound,
    playCustomTTS,
    TTSButton
  };
};

// Hook version for easier use in components
export const useTTS = (options = {}) => {
  return TTSController(options);
};