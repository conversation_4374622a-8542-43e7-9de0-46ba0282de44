# TTS Implementation Testing Instructions

## Overview

The text-to-speech implementation has been completed and is ready for testing. The system will announce "tool call" every time <PERSON> uses a tool during conversations.

## Prerequisites

1. **GROQ API Key**: You need a GROQ API key for the TTS functionality
   - Sign up at https://console.groq.com/
   - Generate an API key
   - Add it to your `.env` file: `GROQ_API_KEY=your_api_key_here`

2. **Dependencies**: Already installed via `npm install`

## Test Setup

1. **Environment Configuration**:
   ```bash
   # Copy the example env file
   cp .env.example .env
   
   # Edit .env and add your GROQ API key
   GROQ_API_KEY=your_actual_groq_api_key_here
   ```

2. **Start the Development Server**:
   ```bash
   npm run dev
   ```

## How to Test

### 1. Enable TTS
1. Open the application in your browser
2. Look for the volume icon (🔇) in the input area next to the image upload button
3. Click the volume icon to enable TTS
4. The first click will:
   - Play an "enabled" sound to satisfy browser audio requirements
   - Pre-load the "tool call" audio
   - Change the icon to green volume (🔊)

### 2. Test Tool Call Announcements
1. With TTS enabled, send any message that will trigger tool usage
2. Examples of messages that typically trigger tools:
   - "List the files in this directory" (triggers Bash tool)
   - "Read the package.json file" (triggers Read tool)
   - "Create a new file called test.txt" (triggers Write tool)
   - "Search for all JavaScript files" (triggers Glob tool)

3. You should hear "tool call" spoken each time Claude uses a tool

### 3. Verify UI Behavior
- **TTS Button States**:
  - 🔇 (gray) = TTS disabled
  - 🔊 (green) = TTS enabled
  - ⏳ (spinning) = Loading/initializing
- **Persistence**: TTS preference is saved to localStorage
- **Error Handling**: If GROQ_API_KEY is missing, you'll see error messages in console

## API Endpoints for Manual Testing

You can test the TTS service directly:

1. **Tool Call Audio**: `GET /api/tts/tool-call`
2. **Enable Sound**: `GET /api/tts/enable-sound`  
3. **Custom TTS**: `POST /api/tts/custom` with body `{"text": "test message"}`
4. **Cache Info**: `GET /api/tts/cache-info`

Example:
```bash
curl http://localhost:3000/api/tts/tool-call -o tool-call-test.wav
```

## Troubleshooting

### Common Issues

1. **No Sound Playing**:
   - Ensure GROQ_API_KEY is set in .env
   - Check browser console for errors
   - Verify browser allows audio playback
   - Try clicking TTS button again

2. **TTS Button Not Appearing**:
   - Check browser console for React errors
   - Ensure the import path is correct
   - Refresh the page

3. **Audio Permission Errors**:
   - Modern browsers require user interaction before playing audio
   - Make sure to click the TTS button first
   - Check browser's audio/autoplay settings

4. **GROQ API Errors**:
   - Verify API key is valid
   - Check GROQ API quota/limits
   - Look at server logs for detailed error messages

### Debug Information

- **Server Logs**: Check terminal running `npm run dev`
- **Browser Console**: Check for JavaScript errors
- **Network Tab**: Verify API calls to `/api/tts/*` endpoints
- **Cache Info**: Visit `/api/tts/cache-info` to see cached audio files

## File Structure

```
server/
├── services/tts.js          # TTS service implementation
└── index.js                 # Updated with TTS endpoints

src/
└── components/
    ├── TTSController.jsx     # TTS controller component
    └── ChatInterface.jsx     # Updated with TTS integration

temp/                         # Cached TTS audio files
├── tool-call.wav            # Cached "tool call" audio
└── enabled.wav              # Cached "enabled" audio
```

## Future Expansion

This implementation is designed for future expansion to use `voice_response` MCP tool input:

1. The `/api/tts/custom` endpoint already supports custom text
2. The `generateCustomSpeech()` method in TTS service is ready
3. Tool call detection can be extended to check for specific tool names
4. Text extraction from tool inputs is straightforward

Example future modification in ChatInterface.jsx:
```javascript
// Instead of always playing "tool call"
if (ttsEnabled) {
  if (part.name === 'voice_response' && part.input?.text) {
    // Use custom TTS with voice_response input text
    playCustomTTS(part.input.text);
  } else {
    // Default "tool call" sound
    playToolCallSound();
  }
}
```

## Success Criteria

✅ TTS button appears in chat interface  
✅ Clicking TTS button enables audio with initial sound  
✅ Tool calls trigger "tool call" audio announcement  
✅ TTS preference persists across browser sessions  
✅ Error handling for missing API keys  
✅ Audio caching for performance  
✅ Clean UI integration  

The implementation is now ready for testing and the future `voice_response` integration!